<template>
    <tui-bottom-popup :show="show" @close="handleClose" :z-index="1000" :height="557 * 2">
        <view class="shopInfo-popup">
            <!-- 头部显示商品信息 -->
            <view class="shopInfo-popup-header">
                <view class="picture-wrapper">
                    <image src="https://weipinshang.oss-cn-shenzhen.aliyuncs.com/78c09202504081350441652.jpg"
                        mode="widthFix"></image>
                </view>
                <view class="good-info-wrapper">
                    <view>
                        <text class="price-symbol">￥</text>
                        <text class="price-value">164.00</text>
                    </view>
                    <view class="good-info-wrapper-bottom">
                        <text class="price-confirm">已选</text>
                        <text class="price-name">原味燕窝胶原饮90g</text>
                    </view>
                </view>
            </view>
            <!-- 商品列表展示部分 -->
            <view class="shopInfo-popup-list">
                <view class="showInfo-popup-list-header">
                    <text>规格</text>
                    <view class="showInfo-popup-list-header-right" @click="handleChangeListType">
                        <image v-if="listType === 'grid'" src="/static/images/order-addcart/list.svg" mode="widthFix">
                        </image>
                        <image v-else src="/static/images/order-addcart/grid-list.svg" mode="widthFix"></image>
                        <text class="list-text">列表</text>
                    </view>
                </view>
                <view class="list-content" :class="listType">
                    <view class="list-content-item" v-for="(item, index) in list" :key="item.id"
                        :class="{ active: activeIndex === index }" @click="handleClick(index)">
                        <view class="list-content-item-top">
                            <image src="https://weipinshang.oss-cn-shenzhen.aliyuncs.com/78c09202504081350441652.jpg"
                                mode="widthFix"></image>
                        </view>
                        <text class="list-content-item-bottom">
                            原味燕窝胶原饮90g
                        </text>
                    </view>
                </view>
            </view>
            <view class="shopInfo-popup-footer">
                <view class="shopInfo-popup-footer-left">数量</view>
                <view class="shopInfo-popup-footer-right">
                    <view class="reduce" :class="item.numSub ? 'on' : ''" @click.stop='subCart(item)'>-</view>
                    <view class='num ml-8 mr-8'>{{ 1 }}</view>
                    <view class="plus" :class="item.numAdd ? 'on' : ''" @click.stop='addCart(item)'>+</view>
                </view>
            </view>
        </view>
    </tui-bottom-popup>
</template>

<script>
import tuiBottomPopup from '@/components/base/tui-bottom-popup.vue';
export default {
    components: {
        tuiBottomPopup
    },
    emits: ['close'],
    data() {
        return {
            list: [
                {
                    id: 1,
                    name: '原味燕窝胶原饮90g',
                    price: 164.00,
                    image: 'https://weipinshang.oss-cn-shenzhen.aliyuncs.com/78c09202504081350441652.jpg'
                },
                {
                    id: 2,
                    name: '原味燕窝胶原饮90g',
                    price: 164.00,
                    image: 'https://weipinshang.oss-cn-shenzhen.aliyuncs.com/78c09202504081350441652.jpg'
                },
                {
                    id: 2,
                    name: '原味燕窝胶原饮90g',
                    price: 164.00,
                    image: 'https://weipinshang.oss-cn-shenzhen.aliyuncs.com/78c09202504081350441652.jpg'
                }
            ],
            activeIndex: 0,
            listType: 'grid'
        }
    },
    props: {
        shopInfo: {
            type: Object,
            default: () => { }
        },
        show: {
            type: Boolean,
            default: false
        }
    },
    methods: {
        handleChangeListType() {
            console.log("2333", this.listType)
            this.listType = this.listType === 'grid' ? 'list' : 'grid';
        },
        handleClose() {
            this.$emit('close');
        },
        handleClick(index) {
            this.activeIndex = index;
        }
    }
}
</script>

<style lang="scss" scoped>
@import "../index.scss";

.shopInfo-popup {
    z-index: 1000;
    height: 100%;
    padding-top: 60rpx;
    padding-left: 30rpx;
    padding-right: 30rpx;
}

// header
.shopInfo-popup-header {
    display: flex;
    align-content: center;
    gap: 14rpx;
    margin-bottom: 46rpx;
}

.good-info-wrapper {
    align-self: flex-end;
    display: flex;
    flex-direction: column;
    gap: 12rpx;

    .price-confirm {
        color: #666;
        font-size: 24rpx;
        font-weight: 400;
    }

    .price-symbol {
        color: #ff0000;
        font-size: 28rpx;
        font-weight: 600;
        font-family: 'PingFang SC', sans-serif;
    }

    .price-value {
        color: #ff0000;
        font-size: 40rpx;
        font-weight: 600;
        font-family: 'PingFang SC', sans-serif;
    }

    .good-info-wrapper-bottom {
        display: flex;
        gap: 12rpx;
        align-items: center;
    }
}

// 商品列表展示部分
.shopInfo-popup-list {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
}

.showInfo-popup-list-header {
    display: flex;
    justify-content: space-between;

    text:nth-child(1) {
        color: #131313;
        font-size: 28rpx;
        font-weight: 500;
    }

    .showInfo-popup-list-header-right {
        display: flex;
        align-content: center;
        gap: 8rpx;

        image {
            width: 32rpx;
            height: 32rpx;
        }

        text {
            color: #131313;
            font-size: 24rpx;
            font-weight: 400;
        }
    }
}

.list-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20rpx;
    border-radius: 16rpx;
    // transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1); // 添加平滑过渡
    will-change: transform; // 优化动画性能
}

.list-content-item {
    display: flex;
    flex-direction: column;
    border-radius: 16rpx;
    min-height: 0;
    border: 2px solid transparent; // 添加透明边框，避免布局跳动
    transition: border-color 0.3s ease; // 只对边框颜色做过渡

    &.active {
        border-color: #F00; // 只改变边框颜色，不改变边框宽度

        .list-content-item-bottom {
            color: #F00;
            transition: color 0.3s ease; // 文字颜色过渡
        }
    }

    .list-content-item-top {
        width: 100%;
        // height: 220rpx;
        overflow: hidden; // 添加 overflow: hidden 来裁剪超出部分
        border-top-left-radius: 16rpx;
        border-top-right-radius: 16rpx;

        image {
            width: 100%;
            height: 100%;

            object-fit: cover; // 添加 object-fit: cover 来保持比例并填充容器
        }
    }

    .list-content-item-bottom {
        flex: 1;
        padding: 8rpx;
        color: #131313;
        font-size: 24rpx;
        font-weight: 400;
        text-align: left;
    }
}

.list-content.list {
    display: flex;
    flex-direction: column;
    gap: 16rpx;

    .list-content-item {
        background-color: #f7f7f7;
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 12rpx;
        width: 100%;
        padding: 2rpx 4rpx;
    }

    .list-content-item-top {
        width: 64rpx;
        height: 64rpx;
        border-top-left-radius: 16rpx;
        border-bottom-left-radius: 16rpx;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        overflow: hidden; // 添加 overflow: hidden 来裁剪超出部分
    }
}

.shopInfo-popup-footer {
    display: flex;
    justify-content: space-between;
    .shopInfo-popup-footer-left{
        
    }
}
</style>